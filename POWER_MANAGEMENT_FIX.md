# ESP32 电源管理配置修复指南

## 🔍 问题分析

**错误信息**：
```
main.cpp:322: Failed to configure power management: ESP_ERR_NOT_SUPPORTED
```

**根本原因**：
- ESP-IDF的电源管理功能需要在编译时通过sdkconfig启用
- 仅在platformio.ini中定义宏是不够的
- Arduino框架下需要正确的配置文件来启用ESP-IDF功能

## ✅ 解决方案

### 1. 创建sdkconfig.defaults文件

已创建 `sdkconfig.defaults` 文件，包含必要的电源管理配置：

```ini
# Power Management
CONFIG_PM_ENABLE=y
CONFIG_PM_DFS_INIT_AUTO=y
CONFIG_PM_USE_RTC_TIMER_REF=y

# FreeRTOS
CONFIG_FREERTOS_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_VTASKDELAY_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_IDLE_TIME_BEFORE_SLEEP=2

# CPU Frequency Scaling
CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80=y
```

### 2. 更新platformio.ini配置

```ini
[env:esp32-c3-devkitm-1]
build_flags = 
    ${common.build_flags}
    -DCONFIG_PM_ENABLE=1
    -DCONFIG_FREERTOS_USE_TICKLESS_IDLE=1
    -DCONFIG_FREERTOS_VTASKDELAY_USE_TICKLESS_IDLE=1
    -DCONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE=1
    -DCONFIG_FREERTOS_IDLE_TIME_BEFORE_SLEEP=2
    -DCONFIG_PM_DFS_INIT_AUTO=1
    -DCONFIG_PM_USE_RTC_TIMER_REF=1
    -DCONFIG_ESP_SLEEP_POWER_DOWN_FLASH=1
board_build.embed_files = sdkconfig.defaults
board_build.esp_idf_sdkconfig_path = sdkconfig.defaults
```

### 3. 创建电源管理工具库

创建了专用的电源管理库：
- `lib/utils/include/power_management.h` - 头文件
- `lib/utils/src/power_management.c` - 实现文件

### 4. 更新main.cpp

```cpp
#include "power_management.h"

void setup() {
    // 其他初始化代码...
    
    // 配置电源管理
    esp_err_t pm_err = power_management_init(80, 40, true);
    if (pm_err != ESP_OK) {
        TY_LOGW("Power management initialization failed, continuing without PM...");
    }
    
    // 其他代码...
}
```

## 🚀 关键特性

### 智能错误处理
- 自动检测电源管理是否可用
- 详细的错误信息和解决建议
- 优雅降级，即使PM失败也能继续运行

### 跨版本兼容
- 支持不同ESP-IDF版本
- 条件编译确保兼容性
- 清晰的配置状态报告

### 调试友好
- 详细的日志输出
- 配置状态检查
- 错误原因分析

## 📋 使用步骤

### 1. 清理并重新编译
```bash
pio run --target clean
pio run
```

### 2. 检查日志输出
正常情况下应该看到：
```
[PowerMgmt] Initializing power management...
[PowerMgmt] Max freq: 80 MHz, Min freq: 40 MHz, Light sleep: enabled
[PowerMgmt] Power management configured successfully
[PowerMgmt] === Power Management Status ===
[PowerMgmt] Available: Yes
[PowerMgmt] Current CPU freq: 80 MHz
[PowerMgmt] CONFIG_PM_ENABLE: Enabled
```

### 3. 如果仍然失败
检查日志中的详细错误信息：
- `CONFIG_PM_ENABLE: Disabled` - 需要正确配置sdkconfig
- `Power management not supported` - 检查ESP-IDF版本
- `Invalid power management parameters` - 检查频率设置

## 🔧 故障排除

### 问题1：仍然报告 ESP_ERR_NOT_SUPPORTED
**解决方案**：
1. 确保 `sdkconfig.defaults` 文件在项目根目录
2. 完全清理项目：`pio run --target clean`
3. 删除 `.pio` 目录
4. 重新编译：`pio run`

### 问题2：配置文件不生效
**解决方案**：
1. 检查 `platformio.ini` 中的路径设置
2. 确保文件名正确：`sdkconfig.defaults`
3. 检查文件编码为UTF-8

### 问题3：编译错误
**解决方案**：
1. 确保 `power_management.h` 在include路径中
2. 检查 `lib/utils` 是否在 `lib_deps` 中
3. 验证所有头文件包含正确

## 📊 预期效果

修复后，您应该看到：
- ✅ 电源管理成功初始化
- ✅ CPU频率动态调整
- ✅ 空闲时自动进入轻度睡眠
- ✅ 详细的状态日志输出

## 🎯 下一步

1. 测试电源管理功能是否正常工作
2. 监控功耗是否有所降低
3. 验证系统稳定性
4. 根据需要调整频率参数

现在重新编译和运行项目，电源管理错误应该已经解决！
