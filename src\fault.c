#include "tuya_log.h"
#include "fault.h"
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"

// --- 故障状态变量 ---
volatile char current_fault_code = 0x00; // 初始化为无故障状态
static SemaphoreHandle_t fault_mutex;

// --- 功能函数实现 ---
void fault_init(void)
{
    fault_mutex = xSemaphoreCreateMutex();
}

void set_fault(unsigned char fault_bit)
{
    xSemaphoreTake(fault_mutex, portMAX_DELAY);
    current_fault_code |= fault_bit;
    xSemaphoreGive(fault_mutex);
    //TY_LOGD("Fault set: 0x%02X. Current faults: 0x%02X\n", fault_bit, current_fault_code);
}

void clear_fault(unsigned char fault_bit)
{
    xSemaphoreTake(fault_mutex, portMAX_DELAY);
    current_fault_code &= (~fault_bit);
    xSemaphoreGive(fault_mutex);
    //TY_LOGD("Fault cleared: 0x%02X. Current faults: 0x%02X\n", fault_bit, current_fault_code);
}

unsigned char get_current_fault_code(void)
{
    unsigned char code;
    xSemaphoreTake(fault_mutex, portMAX_DELAY);
    code = current_fault_code;
    xSemaphoreGive(fault_mutex);
    return code;
}

bool is_fault_active(unsigned char fault_bit)
{
    bool active;
    xSemaphoreTake(fault_mutex, portMAX_DELAY);
    active = ( (current_fault_code & fault_bit) != 0 );
    xSemaphoreGive(fault_mutex);
    return active;
}
