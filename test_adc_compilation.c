/*
 * 测试ADC迁移代码的编译兼容性
 * 这个文件用于验证条件编译是否正确工作
 */

#include "lib/esp32_port/include/esp32_adc.h"
#include <stdio.h>

// 测试函数：验证ADC配置结构体
void test_adc_config_structure(void)
{
    adc_config_t test_config = {
        .voltage_divider_factor = 1.0,
        .adc_pin = 2,
        .adc_unit = ADC_UNIT_1,
        .adc_channel = ADC_CHANNEL_2,
        .adc_atten = ADC_ATTEN_DB_11,
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
        .adc_width = ADC_BITWIDTH_12,
#else
        .adc_width = ADC_WIDTH_BIT_12,
#endif
        .default_vref = 1100,
        .adc1_chan_mask = BIT(2),
        .adc2_chan_mask = 0,
        .channel = {ADC_CHANNEL_2},
    };
    
    printf("ADC配置结构体测试通过\n");
    printf("ESP-IDF版本: %d.%d.%d\n", 
           ESP_IDF_VERSION_MAJOR, 
           ESP_IDF_VERSION_MINOR, 
           ESP_IDF_VERSION_PATCH);
           
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    printf("编译目标: ESP-IDF 5.0+\n");
    printf("使用新版ADC API\n");
#else
    printf("编译目标: ESP-IDF 4.x\n");
    printf("使用旧版ADC API\n");
#endif
}

// 测试函数：验证API函数声明
void test_adc_api_declarations(void)
{
    adc_config_t config;
    
    // 测试函数指针，验证函数声明正确
    void (*calibration_func)(adc_config_t *) = adc_calibration;
    void (*single_init_func)(adc_config_t *) = adc_single_init;
    void (*continuous_init_func)(adc_config_t *) = adc_continuous_init;
    void (*deinit_func)(adc_config_t *) = adc_deinit;
    float (*read_single_func)(adc_config_t *) = ReadVoltageSingle;
    float (*read_continuous_func)(adc_config_t *) = ReadVoltageContinuous;
    
    printf("ADC API函数声明测试通过\n");
    
    // 避免未使用变量警告
    (void)calibration_func;
    (void)single_init_func;
    (void)continuous_init_func;
    (void)deinit_func;
    (void)read_single_func;
    (void)read_continuous_func;
    (void)config;
}

// 主测试函数
int main(void)
{
    printf("=== ESP32 ADC迁移代码编译测试 ===\n");
    
    test_adc_config_structure();
    test_adc_api_declarations();
    
    printf("所有测试通过！\n");
    printf("ADC迁移代码编译兼容性验证成功\n");
    
    return 0;
}
