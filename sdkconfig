#
# ESP32-C3 Power Management Configuration
#

# Power Management
CONFIG_PM_ENABLE=y
CONFIG_PM_DFS_INIT_AUTO=y
CONFIG_PM_USE_RTC_TIMER_REF=y
CONFIG_PM_PROFILING=y
CONFIG_PM_TRACE=y

# FreeRTOS
CONFIG_FREERTOS_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_VTASKDELAY_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_IDLE_TIME_BEFORE_SLEEP=2

# CPU Frequency Scaling
CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE=y
CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_PERFORMANCE=n
CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_ONDEMAND=n

# Light Sleep
CONFIG_ESP_SLEEP_POWER_DOWN_FLASH=y
CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND=y
CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND=y
CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND=y

# Clock Configuration
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=80

# ADC Configuration
CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM=y
CONFIG_ADC_CONTINUOUS_ISR_IRAM_SAFE=y

# WiFi Power Save
CONFIG_ESP_WIFI_SLP_IRAM_OPT=y
CONFIG_ESP_WIFI_FTM_ENABLE=y

# Bluetooth Low Energy
CONFIG_BT_ENABLED=y
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y

# Memory optimization
CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP=y

# Watchdog
CONFIG_ESP_TASK_WDT_EN=y
CONFIG_ESP_TASK_WDT_INIT=y
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5

# Log level
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=3
