/*
 * ESP32 ADC 迁移示例
 * 展示如何使用支持ESP-IDF 4.x和5.x的ADC库
 */

#include "esp32_adc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <stdio.h>

// ADC配置示例
static adc_config_t adc_config = {
    .voltage_divider_factor = 1.0,  // 无分压
    .adc_pin = 2,                   // GPIO2 (ADC1_CH2)
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_2,
    .adc_atten = ADC_ATTEN_DB_11,   // 0-3.3V范围
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    .adc_width = ADC_BITWIDTH_12,   // 新版API
#else
    .adc_width = ADC_WIDTH_BIT_12,  // 旧版API
#endif
    .default_vref = 1100,           // 默认参考电压
    .adc1_chan_mask = BIT(2),       // ADC1_CH2
    .adc2_chan_mask = 0,
    .channel = {ADC1_CHANNEL_2},
};

void adc_single_example_task(void *pvParameters)
{
    printf("=== ADC单次采样示例 ===\n");
    
    // 初始化ADC
    adc_single_init(&adc_config);
    adc_calibration(&adc_config);
    
    printf("ESP-IDF版本: %d.%d.%d\n", 
           ESP_IDF_VERSION_MAJOR, 
           ESP_IDF_VERSION_MINOR, 
           ESP_IDF_VERSION_PATCH);
    
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    printf("使用新版ADC API (ESP-IDF 5.0+)\n");
    printf("校准状态: %s\n", adc_config.cali_enabled ? "已启用" : "未启用");
#else
    printf("使用旧版ADC API (ESP-IDF 4.x)\n");
    printf("校准类型: %d\n", adc_config.cal_type);
#endif
    
    while (1) {
        float voltage = ReadVoltageSingle(&adc_config);
        printf("ADC电压: %.3f V\n", voltage);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

void adc_continuous_example_task(void *pvParameters)
{
    printf("=== ADC连续采样示例 ===\n");
    
    // 初始化ADC连续采样
    adc_continuous_init(&adc_config);
    adc_calibration(&adc_config);
    
    int sample_count = 0;
    while (sample_count < 10) {  // 只采样10次作为示例
        float voltage = ReadVoltageContinuous(&adc_config);
        printf("连续采样 #%d: %.3f V\n", sample_count + 1, voltage);
        sample_count++;
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    // 清理资源
    adc_deinit(&adc_config);
    printf("ADC连续采样示例完成\n");
    vTaskDelete(NULL);
}

void app_main(void)
{
    printf("ESP32 ADC迁移示例启动\n");
    printf("支持ESP-IDF 4.x和5.x版本\n\n");
    
    // 创建单次采样任务
    xTaskCreate(adc_single_example_task, "adc_single", 4096, NULL, 5, NULL);
    
    // 延迟后创建连续采样任务
    vTaskDelay(pdMS_TO_TICKS(5000));
    xTaskCreate(adc_continuous_example_task, "adc_continuous", 4096, NULL, 5, NULL);
}
