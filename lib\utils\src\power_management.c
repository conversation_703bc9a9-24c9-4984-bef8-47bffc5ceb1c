/*
 * ESP32 电源管理工具实现
 */

#include "power_management.h"
#include "esp_pm.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_clk.h"

static const char* TAG = "PowerMgmt";

bool power_management_available(void)
{
#ifdef CONFIG_PM_ENABLE
    return true;
#else
    return false;
#endif
}

esp_err_t power_management_init(uint32_t max_freq_mhz, uint32_t min_freq_mhz, bool enable_light_sleep)
{
#ifdef CONFIG_PM_ENABLE
    ESP_LOGI(TAG, "Initializing power management...");
    ESP_LOGI(TAG, "Max freq: %lu MHz, Min freq: %lu MHz, Light sleep: %s", 
             max_freq_mhz, min_freq_mhz, enable_light_sleep ? "enabled" : "disabled");
    
    // 验证频率参数
    if (max_freq_mhz < min_freq_mhz) {
        ESP_LOGE(TAG, "Max frequency (%lu) cannot be less than min frequency (%lu)", 
                 max_freq_mhz, min_freq_mhz);
        return ESP_ERR_INVALID_ARG;
    }
    
    // 检查频率是否在合理范围内
    if (max_freq_mhz > 160 || min_freq_mhz < 10) {
        ESP_LOGW(TAG, "Frequency values may be out of optimal range (max: %lu, min: %lu)", 
                 max_freq_mhz, min_freq_mhz);
    }
    
    esp_pm_config_t pm_config = {
        .max_freq_mhz = max_freq_mhz,
        .min_freq_mhz = min_freq_mhz,
        .light_sleep_enable = enable_light_sleep
    };
    
    esp_err_t err = esp_pm_configure(&pm_config);
    if (err == ESP_OK) {
        ESP_LOGI(TAG, "Power management configured successfully");
        power_management_print_status();
    } else {
        ESP_LOGE(TAG, "Failed to configure power management: %s (0x%x)", 
                 esp_err_to_name(err), err);
        
        // 提供详细的错误信息
        switch (err) {
            case ESP_ERR_NOT_SUPPORTED:
                ESP_LOGE(TAG, "Power management not supported. Check CONFIG_PM_ENABLE in sdkconfig");
                break;
            case ESP_ERR_INVALID_ARG:
                ESP_LOGE(TAG, "Invalid power management parameters");
                break;
            case ESP_ERR_NOT_FOUND:
                ESP_LOGE(TAG, "Power management component not found");
                break;
            default:
                ESP_LOGE(TAG, "Unknown power management error");
                break;
        }
    }
    
    return err;
#else
    ESP_LOGW(TAG, "Power management not enabled in configuration (CONFIG_PM_ENABLE=n)");
    ESP_LOGI(TAG, "To enable power management:");
    ESP_LOGI(TAG, "1. Add CONFIG_PM_ENABLE=y to sdkconfig.defaults");
    ESP_LOGI(TAG, "2. Add -DCONFIG_PM_ENABLE=1 to build_flags in platformio.ini");
    ESP_LOGI(TAG, "3. Clean and rebuild the project");
    return ESP_ERR_NOT_SUPPORTED;
#endif
}

uint32_t power_management_get_cpu_freq(void)
{
    return esp_clk_cpu_freq() / 1000000; // 转换为MHz
}

void power_management_print_status(void)
{
    ESP_LOGI(TAG, "=== Power Management Status ===");
    ESP_LOGI(TAG, "Available: %s", power_management_available() ? "Yes" : "No");
    ESP_LOGI(TAG, "Current CPU freq: %lu MHz", power_management_get_cpu_freq());
    
#ifdef CONFIG_PM_ENABLE
    ESP_LOGI(TAG, "CONFIG_PM_ENABLE: Enabled");
#else
    ESP_LOGI(TAG, "CONFIG_PM_ENABLE: Disabled");
#endif

#ifdef CONFIG_FREERTOS_USE_TICKLESS_IDLE
    ESP_LOGI(TAG, "CONFIG_FREERTOS_USE_TICKLESS_IDLE: Enabled");
#else
    ESP_LOGI(TAG, "CONFIG_FREERTOS_USE_TICKLESS_IDLE: Disabled");
#endif

#ifdef CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE
    ESP_LOGI(TAG, "CPU Governor: Power Save");
#elif defined(CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_PERFORMANCE)
    ESP_LOGI(TAG, "CPU Governor: Performance");
#else
    ESP_LOGI(TAG, "CPU Governor: Unknown");
#endif

    ESP_LOGI(TAG, "==============================");
}
