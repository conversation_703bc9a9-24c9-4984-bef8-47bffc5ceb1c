/*
 * ESP32 电源管理工具
 * 提供跨版本兼容的电源管理配置
 */

#pragma once

#include <Arduino.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 初始化电源管理
 * @param max_freq_mhz 最大CPU频率 (MHz)
 * @param min_freq_mhz 最小CPU频率 (MHz) 
 * @param enable_light_sleep 是否启用轻度睡眠
 * @return ESP_OK 成功, 其他值表示失败
 */
esp_err_t power_management_init(uint32_t max_freq_mhz, uint32_t min_freq_mhz, bool enable_light_sleep);

/**
 * 检查电源管理是否可用
 * @return true 如果电源管理可用, false 否则
 */
bool power_management_available(void);

/**
 * 获取当前CPU频率
 * @return 当前CPU频率 (MHz)
 */
uint32_t power_management_get_cpu_freq(void);

/**
 * 打印电源管理状态
 */
void power_management_print_status(void);

#ifdef __cplusplus
}
#endif
