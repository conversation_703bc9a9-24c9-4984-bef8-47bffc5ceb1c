# ESP32-C3 Default Configuration for Power Management
# This file contains the default configuration for ESP32-C3 with power management enabled

#
# Power Management
#
CONFIG_PM_ENABLE=y
CONFIG_PM_DFS_INIT_AUTO=y
CONFIG_PM_USE_RTC_TIMER_REF=y
CONFIG_PM_PROFILING=n
CONFIG_PM_TRACE=n

#
# FreeRTOS
#
CONFIG_FREERTOS_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_VTASKDELAY_USE_TICKLESS_IDLE=y
CONFIG_FREERTOS_IDLE_TIME_BEFORE_SLEEP=2
CONFIG_FREERTOS_HZ=1000

#
# ESP System Settings
#
CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80=y
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ=80

#
# Sleep Configuration
#
CONFIG_ESP_SLEEP_POWER_DOWN_FLASH=y
CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND=y
CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND=y
CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND=y

#
# ADC Configuration
#
CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM=y
CONFIG_ADC_CONTINUOUS_ISR_IRAM_SAFE=y
CONFIG_ADC_CAL_EFUSE_TP_ENABLE=y
CONFIG_ADC_CAL_EFUSE_VREF_ENABLE=y
CONFIG_ADC_CAL_LUT_ENABLE=y

#
# WiFi Configuration
#
CONFIG_ESP_WIFI_SLP_IRAM_OPT=y
CONFIG_ESP_WIFI_FTM_ENABLE=n

#
# Bluetooth Configuration
#
CONFIG_BT_ENABLED=y
CONFIG_BT_BLE_ENABLED=y
CONFIG_BT_CONTROLLER_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=3

#
# Memory Configuration
#
CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP=y

#
# Watchdog Configuration
#
CONFIG_ESP_TASK_WDT_EN=y
CONFIG_ESP_TASK_WDT_INIT=y
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5

#
# Log Configuration
#
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=3

#
# Compiler Options
#
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE=y

#
# ESP32C3-Specific
#
CONFIG_ESP32C3_DEFAULT_CPU_FREQ_80=y
CONFIG_ESP32C3_DEFAULT_CPU_FREQ_MHZ=80
