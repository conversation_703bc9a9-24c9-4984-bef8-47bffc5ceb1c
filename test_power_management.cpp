/*
 * 电源管理测试程序
 * 用于验证电源管理配置是否正确工作
 */

#include <Arduino.h>
#include "power_management.h"

void setup() {
    Serial.begin(115200);
    delay(1000);
    
    Serial.println("=== ESP32 电源管理测试 ===");
    Serial.printf("ESP-IDF版本: %d.%d.%d\n", 
                  ESP_IDF_VERSION_MAJOR, 
                  ESP_IDF_VERSION_MINOR, 
                  ESP_IDF_VERSION_PATCH);
    
    // 检查电源管理是否可用
    Serial.printf("电源管理可用: %s\n", power_management_available() ? "是" : "否");
    
    // 显示当前CPU频率
    Serial.printf("当前CPU频率: %lu MHz\n", power_management_get_cpu_freq());
    
    // 尝试初始化电源管理
    Serial.println("\n--- 初始化电源管理 ---");
    esp_err_t result = power_management_init(80, 40, true);
    
    if (result == ESP_OK) {
        Serial.println("✅ 电源管理初始化成功！");
    } else {
        Serial.printf("❌ 电源管理初始化失败: 0x%x\n", result);
        
        // 提供故障排除建议
        Serial.println("\n🔧 故障排除建议:");
        Serial.println("1. 检查 sdkconfig.defaults 文件是否存在");
        Serial.println("2. 确保 CONFIG_PM_ENABLE=y 在配置中");
        Serial.println("3. 清理项目: pio run --target clean");
        Serial.println("4. 重新编译: pio run");
    }
    
    Serial.println("\n--- 配置检查 ---");
    
#ifdef CONFIG_PM_ENABLE
    Serial.println("✅ CONFIG_PM_ENABLE: 已启用");
#else
    Serial.println("❌ CONFIG_PM_ENABLE: 未启用");
#endif

#ifdef CONFIG_FREERTOS_USE_TICKLESS_IDLE
    Serial.println("✅ CONFIG_FREERTOS_USE_TICKLESS_IDLE: 已启用");
#else
    Serial.println("❌ CONFIG_FREERTOS_USE_TICKLESS_IDLE: 未启用");
#endif

#ifdef CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_POWERSAVE
    Serial.println("✅ CPU调节器: 省电模式");
#elif defined(CONFIG_ESP_SYSTEM_CPU_FREQ_GOVERNOR_PERFORMANCE)
    Serial.println("⚡ CPU调节器: 性能模式");
#else
    Serial.println("❓ CPU调节器: 未知");
#endif

    Serial.println("\n=== 测试完成 ===");
}

void loop() {
    // 每10秒显示一次当前状态
    static unsigned long last_check = 0;
    if (millis() - last_check > 10000) {
        last_check = millis();
        
        Serial.printf("[%lu] CPU频率: %lu MHz, 空闲内存: %lu bytes\n", 
                      millis() / 1000, 
                      power_management_get_cpu_freq(),
                      ESP.getFreeHeap());
    }
    
    // 模拟一些工作负载
    delay(100);
}
