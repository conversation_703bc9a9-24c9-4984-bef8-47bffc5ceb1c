#!/usr/bin/env python3
"""
ESP32 ADC迁移修复脚本
自动修复项目中的ADC配置以兼容ESP-IDF 4.x和5.x

使用方法:
python fix_adc_migration.py [目录路径]

如果不指定目录，将扫描当前目录
"""

import os
import re
import sys
import argparse
from pathlib import Path

# 需要修复的模式
FIXES = [
    # ADC通道类型修复
    {
        'pattern': r'ADC1_CHANNEL_(\d+)',
        'replacement': r'ADC_CHANNEL_\1',
        'description': '修复ADC通道类型 ADC1_CHANNEL_X -> ADC_CHANNEL_X'
    },
    # ADC位宽修复 - 需要条件编译
    {
        'pattern': r'\.adc_width\s*=\s*ADC_WIDTH_BIT_12\s*,',
        'replacement': '''#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    .adc_width = ADC_BITWIDTH_12,
#else
    .adc_width = ADC_WIDTH_BIT_12,
#endif''',
        'description': '修复ADC位宽配置，添加条件编译'
    },
    # 移除类型转换
    {
        'pattern': r'\(adc_channel_t\)\s*ADC1_CHANNEL_(\d+)',
        'replacement': r'ADC_CHANNEL_\1',
        'description': '移除不必要的类型转换'
    }
]

# 支持的文件扩展名
SUPPORTED_EXTENSIONS = {'.c', '.cpp', '.h', '.hpp'}

def find_files(directory):
    """查找需要处理的文件"""
    files = []
    for root, dirs, filenames in os.walk(directory):
        for filename in filenames:
            if Path(filename).suffix in SUPPORTED_EXTENSIONS:
                files.append(os.path.join(root, filename))
    return files

def apply_fixes(content, filename):
    """应用修复到文件内容"""
    modified = False
    changes = []
    
    for fix in FIXES:
        pattern = fix['pattern']
        replacement = fix['replacement']
        
        if re.search(pattern, content):
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                modified = True
                changes.append(fix['description'])
    
    return content, modified, changes

def process_file(filepath):
    """处理单个文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        new_content, modified, changes = apply_fixes(original_content, filepath)
        
        if modified:
            # 备份原文件
            backup_path = filepath + '.bak'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # 写入修复后的内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 修复文件: {filepath}")
            for change in changes:
                print(f"   - {change}")
            print(f"   - 原文件备份为: {backup_path}")
            return True
        else:
            print(f"⏭️  跳过文件: {filepath} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件失败: {filepath}")
        print(f"   错误: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='ESP32 ADC迁移修复脚本')
    parser.add_argument('directory', nargs='?', default='.', 
                       help='要扫描的目录路径 (默认: 当前目录)')
    parser.add_argument('--dry-run', action='store_true',
                       help='仅显示需要修复的文件，不实际修改')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.directory):
        print(f"❌ 目录不存在: {args.directory}")
        sys.exit(1)
    
    print("🔍 ESP32 ADC迁移修复脚本")
    print("=" * 50)
    print(f"扫描目录: {os.path.abspath(args.directory)}")
    print(f"模式: {'预览模式' if args.dry_run else '修复模式'}")
    print()
    
    files = find_files(args.directory)
    print(f"找到 {len(files)} 个文件")
    
    if not files:
        print("没有找到需要处理的文件")
        return
    
    fixed_count = 0
    
    for filepath in files:
        if args.dry_run:
            # 预览模式：只检查不修改
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                _, modified, changes = apply_fixes(content, filepath)
                if modified:
                    print(f"📝 需要修复: {filepath}")
                    for change in changes:
                        print(f"   - {change}")
                    fixed_count += 1
            except Exception as e:
                print(f"❌ 检查文件失败: {filepath} - {e}")
        else:
            # 修复模式：实际修改文件
            if process_file(filepath):
                fixed_count += 1
    
    print()
    print("=" * 50)
    if args.dry_run:
        print(f"📊 预览结果: {fixed_count} 个文件需要修复")
        print("💡 运行时不加 --dry-run 参数来实际修复文件")
    else:
        print(f"✅ 修复完成: {fixed_count} 个文件已修复")
        if fixed_count > 0:
            print("💡 原文件已备份为 .bak 文件")
            print("💡 请测试修复后的代码，确认无误后可删除备份文件")

if __name__ == '__main__':
    main()
