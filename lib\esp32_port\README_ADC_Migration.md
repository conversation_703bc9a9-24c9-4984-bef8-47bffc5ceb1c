# ESP32 ADC 迁移指南

本库提供了一个兼容ESP-IDF 4.x和5.x版本的ADC接口，参考了`network_wrapper.c`第139行的条件编译方式。

## 主要变化

### ESP-IDF 5.0+ 的ADC API变化

1. **头文件变化**：
   - 旧版：`#include "esp_adc_cal.h"` 和 `#include "driver/adc.h"`
   - 新版：`#include "esp_adc/adc_oneshot.h"` 和 `#include "esp_adc/adc_cali.h"`

2. **校准API变化**：
   - 旧版：`esp_adc_cal_characterize()` 和 `esp_adc_cal_raw_to_voltage()`
   - 新版：`adc_cali_create_scheme_curve_fitting()` 和 `adc_cali_raw_to_voltage()`

3. **单次采样变化**：
   - 旧版：`adc1_config_width()` 和 `adc1_get_raw()`
   - 新版：`adc_oneshot_new_unit()` 和 `adc_oneshot_read()`

4. **连续采样变化**：
   - 旧版：`adc_digi_initialize()` 和 `adc_digi_read_bytes()`
   - 新版：`adc_continuous_new_handle()` 和 `adc_continuous_read()`

## 条件编译实现

```c
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    // ESP-IDF 5.0+ 新版API
    #include "esp_adc/adc_oneshot.h"
    #include "esp_adc/adc_cali.h"
#else
    // ESP-IDF 4.x 旧版API
    #include "esp_adc_cal.h"
    #include "driver/adc.h"
#endif
```

## 使用方法

### 1. 配置ADC结构体

```c
static adc_config_t adc_config = {
    .voltage_divider_factor = 1.0,
    .adc_pin = 2,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_2,
    .adc_atten = ADC_ATTEN_DB_11,
#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
    .adc_width = ADC_BITWIDTH_12,   // 新版API
#else
    .adc_width = ADC_WIDTH_BIT_12,  // 旧版API
#endif
    .default_vref = 1100,
};
```

### 2. 单次采样

```c
// 初始化
adc_single_init(&adc_config);
adc_calibration(&adc_config);

// 读取电压
float voltage = ReadVoltageSingle(&adc_config);
printf("电压: %.3f V\n", voltage);

// 清理资源
adc_deinit(&adc_config);
```

### 3. 连续采样

```c
// 初始化
adc_continuous_init(&adc_config);
adc_calibration(&adc_config);

// 读取电压
float voltage = ReadVoltageContinuous(&adc_config);
printf("电压: %.3f V\n", voltage);

// 清理资源
adc_deinit(&adc_config);
```

## API函数说明

| 函数名 | 功能 | 支持版本 |
|--------|------|----------|
| `adc_calibration()` | ADC校准初始化 | 4.x / 5.x |
| `adc_single_init()` | 单次采样初始化 | 4.x / 5.x |
| `adc_continuous_init()` | 连续采样初始化 | 4.x / 5.x |
| `adc_deinit()` | 清理ADC资源 | 4.x / 5.x |
| `ReadVoltageSingle()` | 单次电压读取 | 4.x / 5.x |
| `ReadVoltageContinuous()` | 连续电压读取 | 4.x / 5.x |

## 注意事项

1. **版本检测**：代码会自动检测ESP-IDF版本并使用相应的API
2. **校准支持**：新版API支持曲线拟合和线性拟合两种校准方式
3. **资源管理**：新版API需要手动管理句柄，记得调用`adc_deinit()`
4. **兼容性**：保持了与旧版代码的接口兼容性

## 编译配置

确保在`CMakeLists.txt`或`component.mk`中包含必要的组件：

```cmake
# ESP-IDF 5.x
idf_component_register(
    SRCS "esp32_adc.c"
    INCLUDE_DIRS "include"
    REQUIRES esp_adc driver
)
```

## 示例代码

参考 `examples/adc_migration_example.c` 获取完整的使用示例。
